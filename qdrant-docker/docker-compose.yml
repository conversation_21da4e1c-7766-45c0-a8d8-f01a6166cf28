

services:
  qdrant_primary_node:
    image: qdrant/qdrant:latest
    container_name: qdrant_primary_node
    volumes:
      - ${QDRANT__STORAGE__VOLUME}/primary_node:/qdrant/storage
      - ./config/standalone.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6333:6333"
      - "6334:6334"
    environment:
      - QDRANT__CLUSTER__ENABLED=false
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5

  qdrant_secondary_node:
    image: qdrant/qdrant:latest
    volumes:
      - ./config/standalone.yaml:/qdrant/config/production.yaml:ro
    environment:
      - QDRANT__CLUSTER__ENABLED=false
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5


volumes:
  grafana-data: {}