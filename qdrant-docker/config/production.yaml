# ULTRA HIGH-PERFORMANCE Production Qdrant Configuration - Distributed Multi-Node
log_level: INFO

# Storage configuration optimized for MAXIMUM THROUGHPUT
storage:
  # Where to store all the data
  storage_path: /qdrant/storage

  # Where to store snapshots
  snapshots_path: /qdrant/storage/snapshots

  # Where to store temporary files
  temp_path: /qdrant/storage/temp

  # Store payloads on disk to save RAM for vectors
  on_disk_payload: true

  # REMOVE ALL CONCURRENCY LIMITS for maximum throughput
  update_concurrency: null

  # Write-ahead-log optimized for ULTRA-HIGH-THROUGHPUT batch operations
  wal:
    # MASSIVE WAL segments for maximum batch performance
    wal_capacity_mb: 512
    # Create many segments ahead for ultra-high write throughput
    wal_segments_ahead: 10

  # Performance configuration for MAXIMUM CONCURRENT PROCESSING
  performance:
    # Use ALL available cores for search operations
    max_search_threads: 0

    # UNLIMITED optimization threads for maximum performance
    max_optimization_threads: 0

    # Use ALL available CPUs for optimization
    optimizer_cpu_budget: 0

    # REMOVE ALL RATE LIMITING for maximum throughput
    update_rate_limit: null

    # UNLIMITED concurrent shard transfers for distributed setup
    incoming_shard_transfers_limit: null
    outgoing_shard_transfers_limit: null

    # Enable async scorer for better performance
    async_scorer: true

  # Optimizer configuration for ULTRA-HIGH distributed throughput
  optimizers:
    # VERY aggressive optimization for maximum performance
    deleted_threshold: 0.05

    # VERY low minimum for immediate optimization
    vacuum_min_vector_number: 100

    # SINGLE segment for maximum throughput (no segment overhead)
    default_segment_number: 1

    # MASSIVE segments for ultra-high batch performance
    max_segment_size_kb: 10000000

    # NEVER use memory mapping - keep everything in RAM
    memmap_threshold_kb: null

    # VERY HIGH indexing threshold - delay indexing for max insert speed
    indexing_threshold_kb: 500000

    # IMMEDIATE flushes for maximum write speed
    flush_interval_sec: 1

    # UNLIMITED optimization threads
    max_optimization_threads: null

  # HNSW Index configuration optimized for MAXIMUM INSERTION SPEED
  hnsw_index:
    # Lower M for faster insertions (trade some accuracy for speed)
    m: 8

    # Lower ef_construct for faster index building
    ef_construct: 64

    # VERY HIGH threshold - prefer full scan over HNSW for small datasets
    full_scan_threshold_kb: 100000

    # Use ALL available threads for index building
    max_indexing_threads: 0

    # Keep HNSW index in memory for maximum performance
    on_disk: false

    # Lower M for payload index for speed
    payload_m: 8

  # Collection configuration for MAXIMUM DISTRIBUTED THROUGHPUT
  collection:
    # Replication factor for distributed setup (3 nodes = 2 replicas)
    replication_factor: 2

    # Write consistency for distributed setup
    write_consistency_factor: 1

    # Sharding configuration for distributed deployment
    shard_number: 3  # 3 shards across 3 nodes

    # Default vector configuration for MAXIMUM SPEED
    vectors:
      # Keep ALL vectors in memory for maximum performance
      on_disk: false

    # No quantization for maximum insertion speed
    quantization: null

# Service configuration optimized for MAXIMUM DISTRIBUTED THROUGHPUT
service:
  # MASSIVE request size for ultra-large batch operations
  max_request_size_mb: 2048

  # Use ALL available cores for serving API
  max_workers: 0

  # Bind to all interfaces
  host: 0.0.0.0

  # Standard ports
  http_port: 6333
  grpc_port: 6334

  # Enable CORS for web applications
  enable_cors: true

  # Disable TLS for internal cluster communication
  enable_tls: false

  # Disable client certificate verification
  verify_https_client_certificate: false

# Cluster configuration for ULTRA-HIGH-PERFORMANCE distributed deployment
cluster:
  # Enable clustering
  enabled: true

  # P2P configuration for distributed nodes
  p2p:
    # Port for internal communication between peers
    port: 6335

    # Disable TLS for internal communication
    enable_tls: false

  # Consensus configuration optimized for MAXIMUM distributed performance
  consensus:
    # VERY fast tick period for maximum distributed responsiveness
    tick_period_ms: 25

    # Increase timeouts for better cluster stability
    bootstrap_timeout_sec: 30

# Disable telemetry for maximum performance
telemetry_disabled: true
